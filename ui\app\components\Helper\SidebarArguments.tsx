import LogoutModal from '@/app/molecules/LogoutModal';
import { NavigationMenu, SideBarType } from '@/app/organisms/Sidebar';
import { getAuth, removeAuth } from '@/app/services/identity.service';
import { disconnectCalendarSocket } from '@/app/hooks/useCalendarSocket';
import { disconnectCartSocket } from '@/app/hooks/useCartSocket';
import { useRouter, usePathname } from 'next/navigation';
import { useCallback, useState } from 'react';

const SidebarArguments = (): SideBarType => {
    const pathName = usePathname();
    const router = useRouter();
    const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);

    const navigationMenu: NavigationMenu[] = [
        {
            id: 'home',
            label: 'Dashboard',
            icon: 'Home2',
            link: '/dashboard',
        },
        {
            id: 'patients',
            link: '/patients',
            label: 'Patients',
            icon: 'UserAdd',
        },
        {
            id: 'appointments',
            link: '/appointments',
            label: 'Appointments',
            icon: 'Calendar',
        },
        {
            id: 'diagnostics',
            link: '/diagnostics',
            label: 'Diagnostics',
            icon: 'Receipt2',
        },
        {
            id: 'admin',
            label: 'Admin',
            icon: 'ProfileCircle',
            link: '/admin/clinic-details',
        },
    ];

    const isActiveLink = (link: string) => {
        if (link.startsWith('/admin')) {
            return pathName.startsWith('/admin'); // Matches all /admin routes
        }
        return link === pathName;
    };

    const activeNav = navigationMenu.find((item) =>
        isActiveLink(item.link)
    )?.id;

    const handleLogout = useCallback(
        (event: React.MouseEvent) => {
            event.preventDefault();
            //setIsLogoutModalOpen(true);
            disconnectCalendarSocket(); // Disconnect calendar socket before logout
            disconnectCartSocket(); // Disconnect cart socket before logout
            removeAuth();
            // setIsLogoutModalOpen(false);
            router.push('/signin/pin');
        },
        [router]
    );

    const closeLogoutModal = useCallback(() => {
        setIsLogoutModalOpen(false);
    }, []);

    const confirmLogout = useCallback(() => {
        disconnectCalendarSocket(); // Disconnect calendar socket before logout
        disconnectCartSocket(); // Disconnect cart socket before logout
        removeAuth();
        setIsLogoutModalOpen(false);
        router.push('/signin/pin'); // Adjust the route as needed
    }, [router]);

    const sidebarProps: SideBarType = {
        activeNav,
        isShowClinicHeader: true,
        subHeading: '24th March 24',
        clinicInfo: {
            logo: '/images/clinic-logo.png',
            miniLogo: '/images/clinic-mini-logo.png',
            clinicName: 'Clinic X',
        },
        navigationMenu,
        onNavClick: ({ id, event }) => {
            console.log('id => ' + id, event);
        },
        onLogout: handleLogout,
    };

    return {
        ...sidebarProps,
        LogoutModalComponent: () => (
            <LogoutModal
                isOpen={isLogoutModalOpen}
                onClose={closeLogoutModal}
                handleSignOut={confirmLogout}
            />
        ),
    };
};

export default SidebarArguments;
