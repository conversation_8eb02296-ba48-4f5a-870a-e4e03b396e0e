import { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { io, Socket } from 'socket.io-client';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';

let socket: Socket | null = null;

// Function to disconnect and cleanup the cart socket
export const disconnectCartSocket = () => {
  if (socket) {
    console.log('Cart socket disconnecting:', socket.id);
    socket.disconnect();
    socket = null;
  }
};

interface CartState {
    isAdjustmentInInvoice: boolean;
    discountObject: {
        label: string;
        value: string;
    };
    invoiceAmount: number;
}

export const useCartSocket = (appointmentId: string | null) => {
    const queryClient = useQueryClient();
    const router = useRouter();
    const [showCheckedoutModal, setShowCheckedoutModal] = useState(false);
    const [cartState, setCartState] = useState<CartState>({
        isAdjustmentInInvoice: false,
        discountObject: { label: '0%', value: '0' },
        invoiceAmount: 0,
    });

    // Store the latest state in a ref to ensure we always have the most recent value
    const cartStateRef = useRef<CartState>(cartState);

    // Update ref whenever state changes
    useEffect(() => {
        cartStateRef.current = cartState;
    }, [cartState]);

    useEffect(() => {
        if (!appointmentId) return;

        // Initialize socket connection if not already connected
        if (!socket) {
            socket = io(`${process.env.NEXT_PUBLIC_SOCKET_URL}cart/`, {
                transports: ['websocket'],
                autoConnect: true,
                forceNew: true, // Force new connection instead of reusing existing one
                multiplex: false, // Disable multiplexing to ensure separate connections
            });

            socket.on('connect', () => {
                console.log('Cart socket connected');
            });

            socket.on('disconnect', () => {
                console.log('Cart socket disconnected');
            });

            socket.on('connect_error', (error) => {
                console.error('Cart socket connection error:', error);
            });
        }

        // Connect to socket if not connected
        if (!socket.connected) {
            socket.connect();
        }

        // Join cart room
        socket.emit('joinCart', { appointmentId });

        // Handle initial state sync when joining
        const handleCartStateSync = (state: CartState) => {
            setCartState(state);
        };

        // Handle state updates
        const handleCartStateUpdated = (partialState: Partial<CartState>) => {
            setCartState((prev) => {
                const newState = { ...prev, ...partialState };
                return newState;
            });
        };

        // Setup event listeners
        socket.on('cartStateSync', handleCartStateSync);
        socket.on('cartStateUpdated', handleCartStateUpdated);
        socket.on('cartUpdated', (payload) => {
            queryClient.invalidateQueries({
                queryKey: ['getCartListByCartId'],
            });
            queryClient.invalidateQueries({
                queryKey: ['getCartListForAnAppointment', appointmentId],
            });
        });

        socket.on('cartCheckedOut', (payload) => {
            if (payload.appointmentId === appointmentId) {
                setShowCheckedoutModal(true);
            }
        });

        // Cleanup
        return () => {
            if (socket) {
                socket.off('cartStateSync', handleCartStateSync);
                socket.off('cartStateUpdated', handleCartStateUpdated);
                socket.off('cartUpdated');
                socket.off('cartCheckedOut');
                socket.off('userJoined');
                socket.emit('leaveCart', { appointmentId });
            }
        };
    }, [appointmentId, queryClient, router]);

    // Function to emit cart changes
    const emitCartChange = useCallback(
        (
            action: 'add' | 'delete' | 'update' | 'checkout' | 'updateState',
            value: any,
            state?: Partial<CartState>
        ) => {
            if (socket && appointmentId) {
                console.log('Emitting cart change:', { action, value, state });
                socket.emit('cartChange', {
                    appointmentId,
                    action,
                    value,
                    state,
                });

                // If it's a state update, update local state immediately
                if (action === 'updateState' && state) {
                    setCartState((prev) => ({ ...prev, ...state }));
                }
            } else {
                console.warn('Cannot emit cart change - socket not ready', {
                    socketExists: !!socket,
                    appointmentId,
                });
            }
        },
        [appointmentId]
    );

    // Return memoized values
    const memoizedReturn = useMemo(
        () => ({
            emitCartChange,
            showCheckedoutModal,
            cartState,
        }),
        [emitCartChange, showCheckedoutModal, cartState]
    );

    return memoizedReturn;
};
